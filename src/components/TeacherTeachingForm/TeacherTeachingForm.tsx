'use client'

import { useRef, useState } from 'react'
import CustomButton from '../CustomComponents/CustomButton/CustomButton'
import {
  CountrySelect,
  StateSelect,
  CitySelect,
} from 'react-country-state-city'
import 'react-country-state-city/dist/react-country-state-city.css'
import { toast } from 'react-toastify'
import { createTeacherOnboardingApplication } from '@/lib/actions/account.actions'
import MobileLogin from '../MobileLogin/MobileLogin'
import GreenTick from '../Icons/GreenTick'
import EmailVerify from '../EmailVerify/EmailVerify'
import SuccessfullyFormSubmit from '../SuccessfullyFormSubmit/SuccessfullyFormSubmit'
import CustomDatePicker from '../CustomComponents/CustomDatePicker/CustomDatePicker'
import { useFileUpload } from '@/hooks/useFileUpload'

const TeacherTeachingForm = ({ userData }: { userData: any }) => {
  const userNameArray = userData ? userData.name.split(' ') : ['', '']
  const [formData, setFormData] = useState({
    firstName: userData ? userNameArray[0] : '',
    lastName: userData ? userNameArray[userNameArray.length - 1] : '',
    teachingInterest: '',
    addressLine1: userData ? userData?.BillingAddress?.street : '',
    zipCode: userData ? userData?.BillingAddress?.postalCode : '',
    differentShippingAddress: true,
    shippingAddressLine1: '',
    shippingZipCode: '',
    resume: '',
    pitchClasses: '',
  })
  const [country, setCountry] = useState<any>(
    userData?.BillingAddress?.country
      ? { name: userData?.BillingAddress?.country }
      : null,
  )
  const [currentState, setCurrentState] = useState<any>(
    userData?.BillingAddress?.state
      ? { name: userData?.BillingAddress?.state }
      : null,
  )
  const [currentCity, setCurrentCity] = useState<any>(
    userData?.BillingAddress?.city
      ? { name: userData?.BillingAddress?.city }
      : null,
  )
  const [shippingCountry, setShippingCountry] = useState<any>(null)
  const [shippingState, setShippingState] = useState<any>(null)
  const [shippingCity, setShippingCity] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState(
    userData ? '+' + userData.phoneNumber : '',
  )
  const [isPhoneVerified, setIsPhoneVerified] = useState(
    !!userData?.phoneNumber,
  )
  const [isEmailVerified, setIsEmailVerified] = useState(!!userData?.email)
  const [email, setEmail] = useState(userData ? userData.email : '')
  const resumeInputRef = useRef<HTMLInputElement>(null)
  const [isFormSubmitted, setIsFormSubmitted] = useState<boolean>(false)
  const [dateOfBirth, setDateOfBirth] = useState<string>(
    userData ? userData?.birthday : '',
  )

  const { isUploading: isResumeUploading, handleFileUpload } = useFileUpload({
    filePrefix: '',
    onSuccess: (fileUrl) => {
      setFormData((prev) => ({ ...prev, resume: fileUrl }))
    },
    onError: (error) => {
      console.error('Resume upload error:', error)
    },
  })

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target

    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement
      setFormData({ ...formData, [name]: target.checked })
    } else {
      setFormData({ ...formData, [name]: value })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    if (!isPhoneVerified) {
      toast?.error('Please verify your phone number first!', {
        autoClose: 1500,
      })
      return
    }

    e.preventDefault()
    if (!formData.resume) {
      toast?.error('Please upload your resume first!', { autoClose: 1500 })
      return
    }
    setIsLoading(true)

    try {
      // Here you would typically upload the resume to S3 first
      // For this example, we'll use a placeholder URL
      const resumeS3link = formData?.resume || ''

      await createTeacherOnboardingApplication(
        formData.firstName,
        formData.lastName,
        formData.teachingInterest,
        email,
        phoneNumber,
        dateOfBirth,
        formData.addressLine1,
        currentCity?.name || '',
        currentState?.name || '',
        country?.name || '',
        formData.zipCode,
        formData.differentShippingAddress ? '' : formData.shippingAddressLine1,
        formData.differentShippingAddress ? '' : shippingCity?.name || '',
        formData.differentShippingAddress ? '' : shippingState?.name || '',
        formData.differentShippingAddress ? '' : shippingCountry?.name || '',
        formData.differentShippingAddress ? '' : formData.shippingZipCode,
        resumeS3link,
        formData.pitchClasses,
      )
      setFormData({
        firstName: '',
        lastName: '',
        teachingInterest: '',
        addressLine1: '',
        zipCode: '',
        differentShippingAddress: true,
        shippingAddressLine1: '',
        shippingZipCode: '',
        resume: '',
        pitchClasses: '',
      })
      setCountry(null)
      setCurrentState(null)
      setCurrentCity(null)
      setShippingCountry(null)
      setShippingState(null)
      setShippingCity(null)
      setEmail('')
      setPhoneNumber('')
      setIsEmailVerified(false)
      setIsPhoneVerified(false)
      setIsFormSubmitted(true)
      toast?.success('Application submitted successfully!', { autoClose: 5000 })
      // Add success handling here (e.g., show a success message, redirect, etc.)
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } catch (error) {
      console.error('Error submitting form:', error)
      // Add error handling here (e.g., show error message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      {isFormSubmitted ? (
        <SuccessfullyFormSubmit formTitle="Teacher Onboarding" />
      ) : (
        <div className="max-w-4xl mx-auto p-6">
          <h1 className="text-3xl font-semibold mb-4">
            I Want To Teach at The Muse!
          </h1>

          <p className="text-color-grey-7 mb-6">
            We are always looking for great teachers. If you would like to be
            considered for teaching at The Muse Writers Center, please complete
            our application below:
          </p>

          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">
              Muse Teachers Should Have:
            </h2>
            <ul className="list-disc text-color-grey-7 pl-6 space-y-2">
              <li>An M.F.A. or equivalent experience in your genre;</li>
              <li>Publications in your genre;</li>
              <li>Teaching experience as well as a passion for teaching;</li>
              <li>References, both personal and professional;</li>
              <li>Willingness to promote your class;</li>
              <li>Agreement with our [policies] for behavior;</li>
              <li>Reliability and punctuality;</li>
              <li>Willingness to work with a diverse population;</li>
              <li>
                And experience using computers, email, and online scheduling.
              </li>
            </ul>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="firstName"
                  className="block text-sm font-medium mb-1"
                >
                  First Name
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-gray-300 p-2"
                  placeholder="First name"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="lastName"
                  className="block text-sm font-medium mb-1"
                >
                  Last Name
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full rounded-lg border border-gray-300 p-2"
                  placeholder="Last name"
                  required
                />
              </div>
            </div>
            {isPhoneVerified ? (
              <div className="space-y-1">
                <label htmlFor="fullName" className="block text-sm font-medium">
                  Phone
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={phoneNumber}
                    readOnly={true}
                    className="text-sm w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-300"
                    placeholder="Enter your number"
                    required
                  />
                  <span className="absolute right-3 top-3 text-sm font-semibold text-color-green-4 flex items-center gap-2 cursor-pointer">
                    <GreenTick />
                    Verified
                  </span>
                </div>
              </div>
            ) : (
              <div className="teacher-onboarding">
                <MobileLogin
                  showOtpBtn={false}
                  setIsPhoneVerified={setIsPhoneVerified}
                  setUserPhone={setPhoneNumber}
                />
              </div>
            )}
            <EmailVerify
              user={null}
              email={email}
              setEmail={setEmail}
              phoneNumber={phoneNumber}
              studentOnBoarding={false}
              setIsEmailVerified={setIsEmailVerified}
              isEmailVerified={isEmailVerified}
            />
            <CustomDatePicker
              title="Date of Birth"
              setDateOfBirth={setDateOfBirth}
              dateOfBirth={dateOfBirth}
            />

            {/* Teaching Interests */}
            <div className="mt-8">
              <h3 className="text-sm font-bold mb-3">Teaching Interests</h3>
              <div className="space-y-2">
                {[
                  'Adult Program',
                  'Youth Program',
                  'Teen Fellowship Program',
                  'Community Outreach',
                  'Senior Outreach',
                  'Military Outreach',
                ].map((interest) => (
                  <div key={interest} className="flex items-center">
                    <input
                      type="radio"
                      id={interest.replace(/\s+/g, '')}
                      name="teachingInterest"
                      value={interest}
                      checked={formData.teachingInterest === interest}
                      onChange={handleInputChange}
                      className="mr-2"
                    />
                    <label
                      htmlFor={interest.replace(/\s+/g, '')}
                      className="text-sm"
                    >
                      {interest}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Address */}
            <div className="mt-8">
              <h3 className="text-lg font-bold mb-3">Address</h3>
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="addressLine1"
                    className="block text-sm font-medium mb-1"
                  >
                    Address Line 1
                  </label>
                  <input
                    type="text"
                    id="addressLine1"
                    name="addressLine1"
                    value={formData.addressLine1}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-gray-300 p-2"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 teacher-onboarding">
                  <div>
                    <label
                      htmlFor="country"
                      className="block text-sm font-bold"
                    >
                      Country
                    </label>
                    <CountrySelect
                      containerClassName="mt-1"
                      inputClassName=""
                      onChange={(_country: any) => setCountry(_country)}
                      onFocus={(e) => e.target.click()}
                      placeHolder="Select Country"
                      defaultValue={country}
                    />
                  </div>

                  <div>
                    <label htmlFor="state" className="block text-sm font-bold">
                      State
                    </label>
                    <StateSelect
                      countryid={country?.id}
                      containerClassName="mt-1"
                      inputClassName=""
                      onChange={(_state) => setCurrentState(_state)}
                      onFocus={(e) => e.target.click()}
                      defaultValue={currentState}
                      placeHolder="Select State"
                    />
                  </div>
                  <div>
                    <label htmlFor="city" className="block text-sm font-bold">
                      City
                    </label>
                    <CitySelect
                      countryid={country?.id}
                      stateid={currentState?.id}
                      containerClassName="mt-1"
                      inputClassName=""
                      onChange={(_city) => setCurrentCity(_city)}
                      onFocus={(e) => e.target.click()}
                      defaultValue={currentCity}
                      placeHolder="Select City"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="zipCode"
                      className="block text-sm font-medium mb-1"
                    >
                      Zip Code
                    </label>
                    <input
                      type="text"
                      id="zipCode"
                      name="zipCode"
                      value={formData.zipCode}
                      onChange={handleInputChange}
                      className="w-full rounded-lg border border-gray-300 p-2"
                      required
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="differentShippingAddress"
                    name="differentShippingAddress"
                    checked={formData.differentShippingAddress}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <label
                    htmlFor="differentShippingAddress"
                    className="text-sm font-bold"
                  >
                    My shipping address is the same as the billing address.
                  </label>
                </div>
                {!formData.differentShippingAddress && (
                  <div className="mt-8">
                    <h3 className="text-lg font-bold mb-3">Shipping Address</h3>
                    <div className="space-y-4">
                      <div>
                        <label
                          htmlFor="shippingAddressLine1"
                          className="block text-sm font-medium mb-1"
                        >
                          Address Line 1
                        </label>
                        <input
                          type="text"
                          id="shippingAddressLine1"
                          name="shippingAddressLine1" // Fixed name attribute
                          value={formData.shippingAddressLine1}
                          onChange={handleInputChange}
                          className="w-full rounded-lg border border-gray-300 p-2"
                          required
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 teacher-onboarding">
                        <div>
                          <label className="block text-sm font-bold">
                            Country
                          </label>
                          <CountrySelect
                            containerClassName="mt-1"
                            onChange={(_country: any) =>
                              setShippingCountry(_country)
                            }
                            placeHolder="Select Country"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-bold">
                            State
                          </label>
                          <StateSelect
                            countryid={shippingCountry?.id}
                            containerClassName="mt-1"
                            onChange={(_state) => setShippingState(_state)}
                            defaultValue={shippingState}
                            placeHolder="Select State"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-bold">
                            City
                          </label>
                          <CitySelect
                            countryid={shippingCountry?.id}
                            stateid={shippingState?.id}
                            containerClassName="mt-1"
                            onChange={(_city) => setShippingCity(_city)}
                            defaultValue={shippingCity}
                            placeHolder="Select City"
                          />
                        </div>

                        <div>
                          <label
                            htmlFor="shippingZipCode"
                            className="block text-sm font-medium mb-1"
                          >
                            Zip Code
                          </label>
                          <input
                            type="text"
                            id="shippingZipCode"
                            name="shippingZipCode"
                            value={formData.shippingZipCode}
                            onChange={handleInputChange}
                            className="w-full rounded-lg border border-gray-300 p-2"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Resume Upload */}
            <div className="mt-8">
              <h3 className="text-lg font-bold mb-3">Resume Upload</h3>
              <input
                type="file"
                id="resume"
                accept=".pdf,.doc,.docx"
                ref={resumeInputRef}
                name="resume"
                onChange={handleFileUpload}
                className="w-full rounded-lg border border-gray-300 p-2"
              />
            </div>

            {/* Pitch Us a Class */}
            <div className="mt-8">
              <h3 className="text-lg font-bold mb-3">
                Pitch Us a Class or Two
              </h3>
              <textarea
                id="pitchClasses"
                name="pitchClasses"
                value={formData.pitchClasses}
                onChange={handleInputChange}
                className="w-full rounded-lg border border-gray-300 p-2 h-32"
              ></textarea>
            </div>

            <CustomButton
              isLoading={isLoading}
              title="Save and Proceed"
              onClick={handleSubmit}
              isDisabled={isResumeUploading}
              height={12}
            />
          </form>
        </div>
      )}
    </>
  )
}

export default TeacherTeachingForm
