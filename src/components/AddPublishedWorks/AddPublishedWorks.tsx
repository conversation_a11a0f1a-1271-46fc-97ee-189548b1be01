"use client";
import React, { useState } from "react";
import { X } from "lucide-react";

interface Work {
  id: number;
  title: string;
  image: string;
  link?: string;
}

const initialWorks: Work[] = [
  {
    id: 1,
    title: "The Price of Evil",
    image:
      "https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?auto=format&fit=crop&w=800&q=80",
  },
  {
    id: 2,
    title: "Unexpected Journey - A Tale of Two Worlds",
    image:
      "https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?auto=format&fit=crop&w=800&q=80",
  },
];

const PublishedWorks: React.FC = () => {
  const [works, setWorks] = useState<Work[]>(initialWorks);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWork, setEditingWork] = useState<Work | null>(null);

  const [newTitle, setNewTitle] = useState("");
  const [newImage, setNewImage] = useState("");
  const [newLink, setNewLink] = useState("");
  const [previewImage, setPreviewImage] = useState("");

  /** Open modal to add new work */
  const openAddModal = () => {
    setEditingWork(null);
    setNewTitle("");
    setNewImage("");
    setNewLink("");
    setPreviewImage("");
    setIsModalOpen(true);
  };

  /** Open modal to edit work */
  const openEditModal = (work: Work) => {
    setEditingWork(work);
    setNewTitle(work.title);
    setNewImage(work.image);
    setNewLink(work.link || "");
    setPreviewImage(work.image);
    setIsModalOpen(true);
  };

  /** Handle image upload from desktop */
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setPreviewImage(imageUrl);
      setNewImage(imageUrl);
    }
  };

  /** Save or update work */
  const handleSaveWork = () => {
    if (!newTitle.trim() || !previewImage.trim()) return;

    if (editingWork) {
      setWorks((prev) =>
        prev.map((work) =>
          work.id === editingWork.id
            ? {
                ...work,
                title: newTitle.trim(),
                image: previewImage,
                link: newLink.trim(),
              }
            : work
        )
      );
    } else {
      setWorks((prev) => [
        ...prev,
        {
          id: Date.now(),
          title: newTitle.trim(),
          image: previewImage,
          link: newLink.trim(),
        },
      ]);
    }

    setIsModalOpen(false);
  };

  /** Remove work */
  const handleRemoveWork = (id: number) => {
    setWorks((prev) => prev.filter((work) => work.id !== id));
  };

  return (
    <div className="space-y-1">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-sm font-medium">Published Works</h2>
          <p className="text-gray-500 text-xs">
            Showcase your published books and works
          </p>
        </div>
        {/* <button
          onClick={openAddModal}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
        >
          + Add Work
        </button> */}
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {works.map((work) => (
          <div key={work.id} className="flex flex-col cursor-pointer">
            {/* Image Container with Border */}
            <div
              className="relative border rounded-xl overflow-hidden group"
              onClick={() => openEditModal(work)}
            >
              <img
                src={work.image}
                alt={work.title}
                className="w-full h-48 object-cover"
              />

              {/* Remove Button - Only on Hover */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveWork(work.id);
                }}
                className="absolute top-2 right-2 bg-white rounded-full p-1 shadow opacity-0 group-hover:opacity-100 transition"
              >
                <X className="w-5 h-5 text-red-500" />
              </button>
            </div>

            {/* Title below the image - multiline allowed */}
            <p className="mt-2 text-center text-sm font-semibold break-words leading-snug">
              {work.title}
            </p>
          </div>
        ))}

        {/* Add Work Card */}
        <div
          onClick={openAddModal}
          className="border-2 border-dashed border-gray-300 rounded-xl flex flex-col justify-center items-center h-48 cursor-pointer hover:bg-gray-50 transition"
        >
          <span className="text-2xl">+</span>
          <p className="text-gray-500 text-sm">Add Work</p>
        </div>
      </div>

      {/* Custom Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          {/* Overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsModalOpen(false)}
          ></div>

          {/* Modal Box */}
          <div className="relative bg-white rounded-xl shadow-lg p-6 w-full max-w-md z-10">
            {/* Close Button */}
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>

            <h2 className="text-lg font-bold">
              {editingWork ? "Edit Published Work" : "Add Published Work"}
            </h2>
            <p className="text-gray-500 text-sm mb-4">
              {editingWork
                ? "Update the details of your published work."
                : "Add a new published work to your collection."}
            </p>

            {/* Modal Form */}
            <div className="flex flex-col gap-4">
              {/* Title */}
              <div>
                <label className="text-sm font-medium">Title</label>
                <input
                  type="text"
                  placeholder="Enter book title"
                  value={newTitle}
                  onChange={(e) => setNewTitle(e.target.value)}
                  className="border rounded p-2 w-full mt-1 focus:ring focus:ring-blue-200"
                />
              </div>

              {/* Upload Image */}
              {/* <div>
                <label className="text-sm font-medium">Cover Image</label>
                <div className="mt-1 flex flex-col gap-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="border rounded p-2 w-full cursor-pointer"
                  />
                  {previewImage && (
                    <img
                      src={previewImage}
                      alt="Preview"
                      className="rounded-lg h-32 object-cover border"
                    />
                  )}
                </div>
              </div> */}

              {/* Upload Image */}
<div>
  <label className="text-sm font-medium">Cover Image</label>
  <div className="mt-1">
    <div
      className="border-2 border-dashed border-gray-300 rounded-xl flex flex-col items-center justify-center h-32 cursor-pointer hover:bg-gray-50 transition relative"
      onClick={() => document.getElementById("fileInput")?.click()}
    >
      {previewImage ? (
        <img
          src={previewImage}
          alt="Preview"
          className="absolute inset-0 w-full h-full object-cover rounded-xl"
        />
      ) : (
        <div className="flex flex-col items-center">
          <span className="text-3xl text-gray-400">+</span>
          <p className="text-gray-500 text-sm">Upload Image</p>
        </div>
      )}
    </div>

    <input
      id="fileInput"
      type="file"
      accept="image/*"
      onChange={handleImageUpload}
      className="hidden"
    />
  </div>
</div>


              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-2">
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 border rounded-lg hover:bg-gray-100 transition"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveWork}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                >
                  {editingWork ? "Update Work" : "Add Work"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PublishedWorks;
