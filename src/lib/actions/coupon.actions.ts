'use server'

import jsforce from 'jsforce'

import { getAccountId } from '@/lib/actions/account.actions'
import { verifyCustomAccountSession } from '@/lib/actions/login.actions'
import { connectToSalesforce } from '@/lib/salesforce'
import { arrayToSqlInQuery, responseGenerator } from '@/utils/utils'

export const getCouponCodesForUserServer = async (userId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Coupon__c  FROM User_Coupon__c WHERE Account__c = '${userId}' AND Coupon__r.Active__c = true `

  const records = (await conn.query(soql)).records

  const couponIds = records.map((record) => record.Coupon__c)

  return couponIds
}

export const createCouponUsageServer = async (
  userId: string,
  coupon: string,
  discountAmount: number,
  orderId: string,
) => {
  const conn = await connectToSalesforce()

  const newCouponUsageRecord = {
    Coupon__c: coupon,
    Account__c: userId,
    Discounted_Amount__c: discountAmount,
    Used_On__c: jsforce.SfDate.toDateTimeLiteral(new Date()),
    Order__c: orderId,
  }

  conn.sobject('Coupon_Usage__c').create(newCouponUsageRecord)
}

export const validateCouponServer = async (coupon: string, userId: string) => {
  const conn = await connectToSalesforce()

  const soql = `SELECT Id, Per_Person_Usage_Limit__c FROM Coupon__c where Active__c = true AND Discount_Code__c = '${coupon}'`
  const generalCouponRecords = (await conn.query(soql)).records

  if (generalCouponRecords.length > 0) {
    const couponRecord = generalCouponRecords[0]
    const couponId = couponRecord.Id!
    const perPersonLimit = couponRecord.Per_Person_Usage_Limit__c

    // Check per-person usage limit if it exists
    if (perPersonLimit && perPersonLimit > 0) {
      const usageCountSoql = `SELECT COUNT() FROM Coupon_Usage__c WHERE Coupon__c = '${couponId}' AND Account__c = '${userId}'`
      const usageCountResult = await conn.query(usageCountSoql)
      const currentUsageCount = usageCountResult.totalSize

      if (currentUsageCount >= perPersonLimit) {
        throw new Error('Coupon usage limit exceeded for this user')
      }
    }

    return couponId
  }

  return null
}

export const getCouponMetaDataBulkServer = async (couponIds: string[]) => {
  if (!couponIds || couponIds.length === 0) return []
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Description__c from Coupon__c WHERE Id IN ${arrayToSqlInQuery(couponIds)}`

  const records = (await conn.query(soql)).records

  const returnData = records.map((record) => {
    return {
      id: record.Id,
      name: record.Name,
      description: record.Description__c,
    }
  })

  return returnData
}

export const getCouponMetaDataServer = async (couponId: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id, Name, Description__c from Coupon__c WHERE Id = '${couponId}'`

  const records = (await conn.query(soql)).records

  return {
    id: records[0].Id,
    name: records[0].Name,
    description: records[0].Description__c,
  }
}

export const getCouponDataServer = async (coupon: string) => {
  const conn = await connectToSalesforce()
  let soql = `SELECT Id,Name, Coupon_Expiration_Type__c,Semester__c,Discount_Type__c,Discount_Amount__c, Year__c, Discount_Percent__c, Applies_To__c FROM Coupon__c WHERE Active__c = true AND Id = '${coupon}'`

  const records = (await conn.query(soql)).records

  const couponData = {
    id: records[0].Id,
    year: records[0].Year__c,
    semester: records[0].Semester__c,
    discount_type: records[0].Discount_Type__c,
    discount_amount:
      records[0].Discount_Amount__c || records[0].Discount_Percent__c,
    class_type: records[0].Applies_To__c || 'All',
  }

  return couponData
}

export const applyCouponToCartServer = async (
  userId: string,
  discountType: 'Percentage' | 'Fixed Amount',
  discountAmount: string,
  semester?: string,
  year?: string,
  classType?: string,
) => {
  const conn = await connectToSalesforce()

  let soql = `SELECT Id,Product__r.Class__r.Id, Product__r.Class__r.Flexible_Pricing__c, Unit_Price__c FROM Cart_Item__c WHERE Account__c = '${userId}'`

  if (semester) {
    soql += ` AND Product__r.Class__r.Semester__c= '${semester}'`
  }

  if (year) {
    soql += ` AND Product__r.Class__r.Year__c = '${year}'`
  }

  if (classType && classType != 'All') {
    soql += ` AND Product__r.Class__r.RecordType.Name='${classType}'`
  }

  const records = (await conn.query(soql)).records

  const filteredRecords = records.filter((item) => item.Product__r.Class__r)

  // Exclude classes with Flexible_Pricing__c = true from discount calculation
  const discountableRecords = filteredRecords.filter((item) => !item.Product__r.Class__r.Flexible_Pricing__c)

  // Calculate total cart amount for discountable items only
  const totalCartAmount = discountableRecords.reduce((sum, record) => {
    return sum + (record.Unit_Price__c ?? 0)
  }, 0)

  let totalDiscount = 0

  switch (discountType) {
    case 'Percentage':
      const discount = Number(discountAmount)
      totalDiscount = Math.round(totalCartAmount * (discount / 100) * 100) / 100
      break
    case 'Fixed Amount':
      const discount2 = Number(discountAmount)
      totalDiscount =
        Math.round(Math.min(discount2, totalCartAmount) * 100) / 100
      break
  }

  return totalDiscount
}

export const applyCouponIdsToCartBulkServer = async (
  couponIds: string[],
  userId: string,
) => {
  const couponIdTotalDiscountMap = new Map<string, number>()
  for (const couponId of couponIds) {
    const { discount_type, semester, year, class_type, discount_amount } =
      await getCouponDataServer(couponId)
    const totalDiscountAmount = await applyCouponToCartServer(
      userId,
      discount_type,
      discount_amount,
      semester,
      year,
      class_type,
    )
    couponIdTotalDiscountMap.set(couponId, totalDiscountAmount)
  }

  return couponIdTotalDiscountMap
}

export const getCouponIdFromCodeServer = async (couponCode: string) => {
  const conn = await connectToSalesforce()
  const soql = `SELECT Id FROM Coupon__c WHERE Discount_Code__c = '${couponCode}'`

  const records = (await conn.query(soql)).records

  if (records.length === 0) {
    throw new Error('no coupon found')
  }

  return records[0].Id!
}

export const validateCoupon = async (accessToken: string, coupon: string) => {
  try {
    if (!(await verifyCustomAccountSession(accessToken))) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const accessTokenUserId = await getAccountId(accessToken)

    if (!accessTokenUserId) {
      return responseGenerator(false, { errors: ['Unauthorized'] }, true)
    }

    const couponId = await validateCouponServer(coupon, accessTokenUserId)

    if (!couponId) {
      return responseGenerator(false, { errors: ['invalid coupon code'] })
    }

    const couponData = await getCouponDataServer(couponId)

    const couponMetaData = await getCouponMetaDataServer(couponId)

    const totalDiscountAmount = await applyCouponToCartServer(
      accessTokenUserId,
      couponData.discount_type,
      couponData.discount_amount,
      couponData.semester,
      couponData.year,
      couponData.class_type,
    )

    const couponReturnData = {
      id: couponMetaData.id,
      name: couponMetaData.name,
      description: couponMetaData.description,
      totalDiscount: totalDiscountAmount,
    }

    return responseGenerator(true, couponReturnData)
  } catch (error) {
    console.log('Error: ', error)
    return responseGenerator(false, {
      errors: [error instanceof Error ? error.message : String(error)],
    })
  }
}
